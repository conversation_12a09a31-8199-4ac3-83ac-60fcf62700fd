import * as echarts from 'echarts/core';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  PictorialBar<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON><PERSON>,
  EffectScatter<PERSON>hart,
  Gauge<PERSON>hart,
} from 'echarts/charts';

import {
  TitleComponent,
  TooltipComponent,
  Grid<PERSON>omponent,
  Polar<PERSON>omponent,
  Aria<PERSON>omponent,
  ParallelComponent,
  LegendComponent,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  CalendarComponent,
  GraphicComponent,
  GeoComponent,
} from 'echarts/components';

import { SVGRenderer } from 'echarts/renderers';

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  <PERSON>eoComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  MapChart,
  RadarChart,
  SVGRenderer,
  PictorialBarChart,
  RadarComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
  TimelineComponent,
  <PERSON><PERSON>omponent,
  <PERSON><PERSON>omponent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Effect<PERSON><PERSON>ter<PERSON>hart,
  <PERSON>auge<PERSON>hart,
]);

export default echarts;
