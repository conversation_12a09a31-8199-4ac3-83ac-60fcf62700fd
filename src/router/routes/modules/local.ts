import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

/**
 * 这里放本地路由  就是后台没有包含的
 * 默认登录后才能访问
 * 白名单路径: router/guard/permissionGuard.ts
 * 注意 component: LAYOUT 一定要有
 */
export const localRoutes: AppRouteModule[] = [
  {
    path: '/social-callback',
    name: 'socialCallback',
    component: () => import('@/views/auth/social-callback/index.vue'),
    meta: {
      title: '授权登录页',
    },
  },
  {
    path: '/test/drawer',
    name: 'DrawerTest',
    component: () => import('@/views/test/DrawerTest.vue'),
    meta: {
      title: 'Drawer 测试',
    },
  },
  {
    path: '/test/map',
    name: 'MapTest',
    component: () => import('@/views/security/dashboard/test-map-simple.vue'),
    meta: {
      title: '地图测试',
      ignoreAuth: true,
    },
  },
  {
    component: LAYOUT,
    path: '/system/oss-config',
    name: 'OssConfigRoot',
    meta: { title: 'OSS配置管理' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/oss/OssConfig.vue'),
        name: 'OssConfig',
        meta: {
          hidden: true,
          title: 'OSS配置管理',
          currentActiveMenu: '/system/oss',
        },
      },
    ],
  },
  {
    component: LAYOUT,
    path: '/account',
    name: 'AccountInfo',
    redirect: '/setting',
    meta: {
      hideMenu: true,
      title: '账号',
    },
    children: [
      {
        path: 'setting',
        name: 'AccountSettingPage',
        component: () => import('@/views/auth/profile/index.vue'),
        meta: {
          title: '个人设置',
        },
      },
    ],
  },
  {
    component: LAYOUT,
    path: '/system/assign-roles',
    name: 'AssignRolesRoot',
    redirect: '/:roleId',
    meta: {
      hideMenu: true,
      title: '分配角色',
    },
    children: [
      {
        path: ':roleId',
        name: 'AssignRoles',
        component: () => import('@/views/system/role/AssignRoles/index.vue'),
        meta: {
          title: '分配角色',
          currentActiveMenu: '/system/role',
        },
      },
    ],
  },
];
